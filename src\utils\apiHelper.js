import { toast } from '@/plugins/toast'

/**
 * Utility helper for handling paginated API responses
 */
export class ApiHelper {
    /**
     * Build URL with query parameters
     * @param {string} baseUrl - Base API endpoint
     * @param {Object} params - Query parameters
     * @returns {string} Complete URL with query string
     */
    static buildUrl(baseUrl, params = {}) {
        const { page = 1, per_page = 10, search = '', sort_field, sort_direction, ...filters } = params;
        const pageNum = parseInt(page) || 1;
        const limitNum = parseInt(per_page) || 10;

        const searchParams = new URLSearchParams({
            page: pageNum,
            limit: limitNum,
            ...(search?.trim() && { search: search.trim() }),
            ...(sort_field && sort_direction && { sort: sort_field, direction: sort_direction }),
        });

        // Add filters
        Object.entries(filters).forEach(([key, value]) => {
            if (value !== null && value !== undefined && value !== '') {
                searchParams.append(`filter[${key}]`, value);
            }
        });

        return `${baseUrl}?${searchParams}`;
    }

    /**
     * Handle paginated API response
     * @param {Object} response - Axios response object
     * @param {Object} params - Original request parameters
     * @returns {Object} Standardized response format
     */
    static handlePaginatedResponse(response, params = {}) {
        const data = response.data.data;
        const { page = 1, per_page = 10 } = params;
        const pageNum = parseInt(page) || 1;
        const limitNum = parseInt(per_page) || 10;

        return {
            data: data.data || [],
            current_page: data.current_page || pageNum,
            per_page: data.per_page || limitNum,
            total: data.total || 0,
            last_page: data.last_page || 1
        };
    }

    /**
     * Handle API errors with toast notification
     * @param {Error} error - Error object
     * @param {string} message - Custom error message
     * @param {Object} fallbackData - Fallback data structure
     * @returns {Object} Fallback response format
     */
    static handleError(error, message = "Operation failed", fallbackData = {}) {
        console.error(message, error);
        toast.error(message);

        return {
            data: [],
            current_page: 1,
            per_page: 10,
            total: 0,
            last_page: 1,
            ...fallbackData
        };
    }

    /**
     * Complete fetch operation with loading state management
     * @param {Function} apiCall - API call function
     * @param {Object} store - Store object with loading state
     * @param {Object} params - Request parameters
     * @param {string} errorMessage - Custom error message
     * @returns {Promise<Object>} API response
     */
    static async fetchWithLoading(apiCall, store, params = {}, errorMessage = "Failed to fetch data") {
        store.loading = true;
        try {
            const response = await apiCall();
            const result = this.handlePaginatedResponse(response, params);

            // Update store state if it has these properties
            if (store.hasOwnProperty('totalPages')) store.totalPages = result.last_page;
            if (store.hasOwnProperty('totalRecords')) store.totalRecords = result.total;

            return result;
        } catch (error) {
            return this.handleError(error, errorMessage);
        } finally {
            store.loading = false;
        }
    }
}
