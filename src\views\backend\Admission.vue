<script setup>
import {
  onMounted,
  ref,
} from 'vue'

import DataTableProvider from '@/components/DataTable/DataTableProvider.vue'
import SmartDataTable from '@/components/DataTable/SmartDataTable.vue'
import useAdmissionStore from '@/stores/admission'

const admissionStore = useAdmissionStore();
admissionStore.fetchAdmissions();
const pagination = ref({
  current_page: 1,
  per_page: 10,
  total: 0,
  last_page: 1
})

// Column configuration - ONLY these columns will be shown
const columnConfig = {
  id: {
    title: 'ID',
    width: '80px',
    filterable: true
  },
  name: {
    title: 'Full Name',
    width: '200px',
    filterable: true
  },
  email: {
    title: 'Email Address',
    filterable: true
  },
  image_url_full: {
    title: 'Profile Image',
    type: 'image',
    width: '100px',
    filterable: false
  },
  user_position: {
    title: 'Position',
    filterable: true
  },
  status: {
    title: 'Status',
    type: 'badge',
    filterable: true,
    filterType: 'select',
    filterOptions: [
      { value: '', label: 'All Status' },
      { value: '1', label: 'Active' },
      { value: '0', label: 'Inactive' }
    ],
    // Transform raw values (1, 0) to display values (Active, Inactive)
    transform: (value) => {
      if (value === 1 || value === '1') return 'Active'
      if (value === 0 || value === '0') return 'Inactive'
      return value // fallback for other values
    },
    badgeConfig: {
      'Active': 'bg-green-100 text-green-800',
      'Inactive': 'bg-red-100 text-red-800',
      // Keep original values as fallback
      '1': 'bg-green-100 text-green-800',
      '0': 'bg-red-100 text-red-800'
    }
  },
  //add
  // Only these 6 columns will be shown: ID, Name, Email, Image, Position, Status
  // Actions column will be auto-added because we have @edit and @view handlers
  // No other columns will appear (no created_at, updated_at, etc.)
}
// Search and filter states
const searchQuery = ref('')
const sortField = ref('')
const sortDirection = ref('asc');
const admissions = ref([])
const loading = ref(false)
async function Getadmissions(page = 1) {
  loading.value = true
  try {
    const params = {
      page: page,
      per_page: pagination.value.per_page,
      search: searchQuery.value,
      sort_field: sortField.value,
      sort_direction: sortDirection.value
    }

    const response = await admissionStore.fetchAdmissions(params);
    // // Update data and pagination
    admissions.value = response.data || []
    pagination.value = {
      current_page: response.current_page || 1,
      per_page: response.per_page || 10,
      total: response.total || 0,
      last_page: response.last_page || 1
    }

  } catch (error) {
    console.error('Failed to fetch admissions:', error)
    admissions.value = []
  } finally {
    loading.value = false
  }
}
onMounted(() => {
  Getadmissions();
})
</script>
<template>
  <DataTableProvider>
    <div class="p-4">
      <SmartDataTable title="Admission Management" :data="admissions" :loading="loading" :pagination="pagination"
        :column-config="columnConfig" :table-config="tableConfig" :on-delete="handleDeleteAdmission"
        :on-bulk-delete="handleBulkDeleteAdmissions" @edit="handleEdit" @view="handleView" @add="handleAdd"
        @refresh="Getadmissions" @page-change="handlePageChange" @search="handleSearch" @sort="handleSort" />
    </div>
  </DataTableProvider>
</template>
