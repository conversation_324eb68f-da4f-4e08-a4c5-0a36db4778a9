import { defineStore } from 'pinia'

import api from '@/api/api'
import { toast } from '@/plugins/toast'
import { ApiHelper } from '@/utils/apiHelper'

const useAdmissionStore = defineStore("useAdmissionStore", {
    state: () => ({
        admissions: [],      // ← Main data array
        totalPages: 1,       // ← Required for pagination
        totalRecords: 0,     // ← Required for pagination info
        loading: false,
    }),

    actions: {
        // Required method with exact signature for SmartDataTable
        async fetchAdmissions(params = {}) {
            const url = ApiHelper.buildUrl('/admissions', params);
            const result = await ApiHelper.fetchWithLoading(
                () => api.get(url),
                this,
                params,
                "Failed to fetch users"
            );

            this.admissions = result.data;
            return result;
        },

        // Required delete method for SmartDataTable
        async deleteAdmission(id) {
            this.loading = true;
            try {
                const response = await api.delete(`/admissions/${id}`);
                if (response.data) {
                    // Remove from local state
                    this.admissions = this.admissions.filter((admission) => admission.id !== id);
                    toast.success("Admission deleted successfully");
                    return true;
                }
                return false;
            } catch (err) {
                console.error("Error deleting admission:", err);
                toast.error("Failed to delete admission");
                return false;
            } finally {
                this.loading = false;
            }
        },

        // Optional: Additional methods for CRUD operations
        async createAdmission(admissionData) {
            this.loading = true;
            try {
                const response = await api.post("/admissions", admissionData);
                if (response.data) {
                    this.admissions.push(response.data.data.admission);
                    toast.success(response.data.message);
                    return true;
                }
                return false;
            } catch (err) {
                console.error("Error creating admission:", err);
                toast.error(err.response?.data?.message || "Failed to create admission");
                return false;
            } finally {
                this.loading = false;
            }
        },

        async updateAdmission(admissionData) {
            this.loading = true;
            try {
                const response = await api.put(`/admissions/${admissionData.id}`, admissionData);
                if (response.data) {
                    toast.success(response.data.message);
                    // Refresh the admission list to get updated data
                    await this.fetchAdmissions();
                    return true;
                }
                return false;
            } catch (err) {
                console.error("Error updating admission:", err);
                toast.error(err.response?.data?.message || "Failed to update admission");
                return false;
            } finally {
                this.loading = false;
            }
        }
    },
});

export default useAdmissionStore;
