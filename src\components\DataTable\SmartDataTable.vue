<template>
  <div class="smart-datatable w-full">
    <!-- Enhanced Title with Animation and Add Button -->
    <div v-if="title" class="mb-6">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-3xl font-bold text-gray-900 mb-2 animate-fade-in">{{ title }}</h1>
          <div class="h-1 w-20 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-full animate-slide-in"></div>
        </div>

        <!-- Add Button in Header -->
        <button v-if="finalTableConfig.addButton?.enabled" @click="$emit('add')" :class="getAddButtonClasses()"
          class="animate-fade-in">
          <PlusIcon class="h-5 w-5 mr-2" />
          {{ finalTableConfig.addButton.text || 'Add New' }}
        </button>
      </div>
    </div>

    <!-- Enhanced Table Container -->
    <div
      class="bg-white rounded-lg shadow-lg overflow-hidden border border-gray-200 transition-all duration-300 hover:shadow-xl">
      <DataTable :data="displayData" :columns="columns" :config="finalTableConfig" :loading="displayLoading"
        :title="title" :available-filters="availableFilters" :current-filters="currentFilters"
        :server-response="displayPaginationInfo" server-side @sort="handleSort" @filter="handleFilter"
        @clear-filters="handleClearFilters" @page-change="handlePageChange" @size-change="handleSizeChange"
        @action="handleActionEvent" @add="$emit('add')" @refresh="handleRefresh" @bulk-delete="handleBulkDelete" />
    </div>
  </div>
</template>

<script setup>
import {
  computed,
  watch,
} from 'vue'

import { PlusIcon } from '@heroicons/vue/24/outline'

import { useSmartDataTable } from './composables/useSmartDataTable.js'
import DataTable from './DataTable.vue'

// Props
const props = defineProps({
  title: {
    type: String,
    default: ''
  },
  store: {
    type: Object,
    required: false,
    default: null
  },
  // External data props (when not using store)
  data: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  },
  pagination: {
    type: Object,
    default: () => ({})
  },
  fetchMethod: {
    type: String,
    default: 'fetchUser'
  },
  deleteMethod: {
    type: String,
    default: 'deleteUser'
  },
  columnConfig: {
    type: Object,
    default: () => ({})
  },
  tableConfig: {
    type: Object,
    default: () => ({})
  },
  defaultActions: {
    type: Array,
    default: () => ['view', 'edit', 'delete']
  },
  autoColumns: {
    type: Boolean,
    default: true
  },
  autoLoad: {
    type: Boolean,
    default: true
  },
  // Custom delete functions
  onDelete: {
    type: Function,
    default: null
  },
  onBulkDelete: {
    type: Function,
    default: null
  }
})

// Emits
const emit = defineEmits([
  'action', 'edit', 'view', 'delete', 'add',
  'refresh', 'page-change', 'search', 'sort'
])

// Use Smart DataTable composable
const {
  loading: internalLoading,
  data: internalData,
  columns,
  availableFilters,
  serverParams,
  paginationInfo,
  finalTableConfig,
  fetchData,
  handleSort: internalHandleSort,
  handleFilter: internalHandleFilter,
  handleClearFilters: internalHandleClearFilters,
  handlePageChange: internalHandlePageChange,
  handleAction,
  handleRefresh: internalHandleRefresh,
  handleBulkDelete: internalHandleBulkDelete,
  setColumns,
  updateColumnConfig,
  initializeFromExternalData
} = useSmartDataTable({
  store: props.store,
  fetchMethod: props.fetchMethod,
  deleteMethod: props.deleteMethod,
  columnConfig: props.columnConfig,
  tableConfig: props.tableConfig,
  defaultActions: props.defaultActions,
  autoColumns: props.autoColumns,
  autoLoad: props.autoLoad && props.store, // Only auto-load if store is provided
  onDelete: props.onDelete,
  onBulkDelete: props.onBulkDelete
})

// Use external data if provided, otherwise use internal data
const displayData = computed(() => props.data.length > 0 ? props.data : internalData.value)
const displayLoading = computed(() => props.store ? internalLoading.value : props.loading)

// Use external pagination if provided, otherwise use internal pagination
const displayPaginationInfo = computed(() => {
  if (props.store) {
    // Store mode - convert internal pagination info to Laravel format
    return {
      current_page: paginationInfo.currentPage,
      per_page: paginationInfo.pageSize,
      total: paginationInfo.total,
      last_page: paginationInfo.totalPages,
      from: paginationInfo.total > 0 ? ((paginationInfo.currentPage - 1) * paginationInfo.pageSize) + 1 : 0,
      to: Math.min(paginationInfo.currentPage * paginationInfo.pageSize, paginationInfo.total),
      next_page_url: paginationInfo.currentPage < paginationInfo.totalPages ? 'next' : null,
      prev_page_url: paginationInfo.currentPage > 1 ? 'prev' : null
    }
  } else {
    // External data mode - use provided pagination (already in Laravel format)
    return props.pagination && Object.keys(props.pagination).length > 0 ? props.pagination : {
      current_page: 1,
      per_page: 10,
      total: 0,
      last_page: 1,
      from: 0,
      to: 0,
      next_page_url: null,
      prev_page_url: null
    }
  }
})

// Initialize columns from external data when data changes
watch(() => props.data, async (newData) => {
  if (!props.store && newData && newData.length > 0) {
    await initializeFromExternalData(newData)
  }
}, { immediate: true })

// Event handlers - use internal handlers if store provided, otherwise emit events
function handleSort(sortData) {
  if (props.store) {
    return internalHandleSort(sortData)
  } else {
    emit('sort', sortData)
  }
}

function handleFilter(filterData) {
  if (props.store) {
    return internalHandleFilter(filterData)
  } else {
    emit('search', filterData.value) // For global search
  }
}

function handleClearFilters() {
  if (props.store) {
    return internalHandleClearFilters()
  } else {
    emit('refresh')
  }
}

function handlePageChange(pageData) {
  if (props.store) {
    return internalHandlePageChange(pageData)
  } else {
    emit('page-change', pageData.page)
  }
}

function handleSizeChange(size) {
  if (props.store) {
    // Handle page size change for store mode
    return internalHandlePageChange({ page: 1, pageSize: size })
  } else {
    emit('page-change', 1) // Reset to page 1 when size changes
  }
}

function handleRefresh() {
  if (props.store) {
    return internalHandleRefresh()
  } else {
    emit('refresh')
  }
}

function handleBulkDelete(selectedData) {
  if (props.store) {
    return internalHandleBulkDelete(selectedData)
  } else {
    // For external data mode, the delete functions should handle this
    if (props.onBulkDelete) {
      return props.onBulkDelete(selectedData.map(item => item.id), selectedData)
    }
  }
}

// Convert server params filters to DataTable format
const currentFilters = computed(() => {
  const filters = {}
  if (serverParams.filters) {
    Object.entries(serverParams.filters).forEach(([key, value]) => {
      filters[key] = value
    })
  }
  return filters
})

// Handle action events and emit to parent
async function handleActionEvent(actionData) {
  const result = await handleAction(actionData)

  // Emit specific action events
  if (result) {
    emit('action', result)
    emit(result.action, result.row)
  }
}

// Get Add button classes based on configuration
function getAddButtonClasses() {
  const config = finalTableConfig.addButton || {}
  const variant = config.variant || 'primary'
  const size = config.size || 'md'

  // Base classes
  let classes = 'inline-flex items-center border shadow-sm font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 transition-all duration-200 hover:shadow-md'

  // Size classes
  switch (size) {
    case 'sm':
      classes += ' px-3 py-2 text-sm'
      break
    case 'lg':
      classes += ' px-6 py-3 text-lg'
      break
    default: // md
      classes += ' px-4 py-2 text-base'
  }

  // Variant classes
  switch (variant) {
    case 'secondary':
      classes += ' border-gray-300 text-gray-700 bg-white hover:bg-gray-50 focus:ring-gray-500'
      break
    case 'success':
      classes += ' border-transparent text-white bg-green-600 hover:bg-green-700 focus:ring-green-500'
      break
    case 'danger':
      classes += ' border-transparent text-white bg-red-600 hover:bg-red-700 focus:ring-red-500'
      break
    case 'warning':
      classes += ' border-transparent text-white bg-yellow-600 hover:bg-yellow-700 focus:ring-yellow-500'
      break
    case 'info':
      classes += ' border-transparent text-white bg-blue-600 hover:bg-blue-700 focus:ring-blue-500'
      break
    default: // primary
      classes += ' border-transparent text-white bg-indigo-600 hover:bg-indigo-700 focus:ring-indigo-500'
  }

  return classes
}

// Expose methods for parent component
defineExpose({
  fetchData,
  setColumns,
  updateColumnConfig,
  refresh: handleRefresh
})
</script>

<style scoped>
.smart-datatable {
  @apply w-full;
}

/* Title Animations */
@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slide-in {
  from {
    width: 0;
  }

  to {
    width: 5rem;
  }
}

.animate-fade-in {
  animation: fade-in 0.6s ease-out;
}

.animate-slide-in {
  animation: slide-in 0.8s ease-out 0.3s both;
}

/* Container Enhancements */
.smart-datatable {
  animation: fade-in 0.5s ease-out;
}

/* Hover Effects */
.smart-datatable .bg-white:hover {
  transform: translateY(-2px);
}

/* Responsive Enhancements */
@media (max-width: 640px) {
  .smart-datatable h1 {
    font-size: 1.5rem;
  }
}
</style>
