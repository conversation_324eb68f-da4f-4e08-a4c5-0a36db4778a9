import { defineStore } from 'pinia'

import api from '@/api/api'
import { toast } from '@/plugins/toast'
import { ApiHelper } from '@/utils/apiHelper'

const useUserStore = defineStore("useUserStore", {
    state: () => ({
        user: [],
        totalPages: 1,
        totalRecords: 0,
        loading: false,
    }),

    actions: {
        async fetchUser(params = {}) {
            const url = ApiHelper.buildUrl('/user-list', params);
            const result = await ApiHelper.fetchWithLoading(
                () => api.get(url),
                this,
                params,
                "Failed to fetch users"
            );

            this.user = result.data;
            return result;
        },

        // Keep other methods as they are
        async createUser(userData) {
            this.loading = true;
            try {
                const response = await api.post("/createUser", userData);
                if (response.data) {
                    this.user.push(response.data.data.user);
                    toast.success(response.data.message);
                    return true;
                }
                return false;
            } catch (err) {
                console.error("Error creating user:", err);
                toast.error(err.response?.data?.message || "Failed to create user");
                return false;
            } finally {
                this.loading = false;
            }
        },

        async updateUser(userData) {
            this.loading = true;
            try {
                const response = await api.post("/editUser", userData);
                if (response.data) {
                    toast.success(response.data.message);
                    // Refresh the user list to get updated data
                    await this.fetchUser();
                    return true;
                }
                return false;
            } catch (err) {
                console.error("Error updating user:", err);
                toast.error(err.response?.data?.message || "Failed to update user");
                return false;
            } finally {
                this.loading = false;
            }
        },

        async deleteUser(userId) {
            this.loading = true;
            try {
                const response = await api.delete(`/deleteUser/${userId}`);

                if (response.data) {
                    toast.success(response.data.message || "User deleted successfully");
                    return true;
                }
                return false;
            } catch (err) {
                console.error("Error deleting user:", err);
                toast.error(err.response?.data?.message || "Failed to delete user");
                throw err; // Re-throw to let the component handle it
            } finally {
                this.loading = false;
            }
        },

        // Keep other methods unchanged
    },
});

export default useUserStore;
