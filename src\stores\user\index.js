import { defineStore } from 'pinia'

import api from '@/api/api'
import { toast } from '@/plugins/toast'

const useUserStore = defineStore("useUserStore", {
    state: () => ({
        user: [],
        totalPages: 1,
        totalRecords: 0,
        loading: false,
    }),

    actions: {
        async fetchUser(params = {}) {
            this.loading = true;
            try {
                const { page = 1, per_page = 10, search = '', sort_field, sort_direction, ...filters } = params;
                const pageNum = parseInt(page) || 1;
                const limitNum = parseInt(per_page) || 10;

                // Build URL with URLSearchParams for cleaner code
                const searchParams = new URLSearchParams({
                    page: pageNum,
                    limit: limitNum,
                    ...(search?.trim() && { search: search.trim() }),
                    ...(sort_field && sort_direction && { sort: sort_field, direction: sort_direction }),
                });

                // Add filters
                Object.entries(filters).forEach(([key, value]) => {
                    if (value !== null && value !== undefined && value !== '') {
                        searchParams.append(`filter[${key}]`, value);
                    }
                });

                const response = await api.get(`/user-list?${searchParams}`);
                const data = response.data.data;

                // Update store state
                this.user = data.data || [];
                this.totalPages = data.last_page || 1;
                this.totalRecords = data.total || 0;

                return {
                    data: this.user,
                    current_page: data.current_page || pageNum,
                    per_page: data.per_page || limitNum,
                    total: data.total || 0,
                    last_page: data.last_page || 1
                };
            } catch (err) {
                console.error("Error fetching users:", err);
                toast.error("Failed to fetch users");
                this.user = [];
                return { data: [], current_page: 1, per_page: 10, total: 0, last_page: 1 };
            } finally {
                this.loading = false;
            }
        },

        // Keep other methods as they are
        async createUser(userData) {
            this.loading = true;
            try {
                const response = await api.post("/createUser", userData);
                if (response.data) {
                    this.user.push(response.data.data.user);
                    toast.success(response.data.message);
                    return true;
                }
                return false;
            } catch (err) {
                console.error("Error creating user:", err);
                toast.error(err.response?.data?.message || "Failed to create user");
                return false;
            } finally {
                this.loading = false;
            }
        },

        async updateUser(userData) {
            this.loading = true;
            try {
                const response = await api.post("/editUser", userData);
                if (response.data) {
                    toast.success(response.data.message);
                    // Refresh the user list to get updated data
                    await this.fetchUser();
                    return true;
                }
                return false;
            } catch (err) {
                console.error("Error updating user:", err);
                toast.error(err.response?.data?.message || "Failed to update user");
                return false;
            } finally {
                this.loading = false;
            }
        },

        async deleteUser(userId) {
            this.loading = true;
            try {
                const response = await api.delete(`/deleteUser/${userId}`);

                if (response.data) {
                    toast.success(response.data.message || "User deleted successfully");
                    return true;
                }
                return false;
            } catch (err) {
                console.error("Error deleting user:", err);
                toast.error(err.response?.data?.message || "Failed to delete user");
                throw err; // Re-throw to let the component handle it
            } finally {
                this.loading = false;
            }
        },

        // Keep other methods unchanged
    },
});

export default useUserStore;
