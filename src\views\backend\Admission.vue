<script setup>
import DataTableProvider from '@/components/DataTable/DataTableProvider.vue'
import SmartDataTable from '@/components/DataTable/SmartDataTable.vue'
import useAdmissionStore from '@/stores/admission'

const admissionStore = useAdmissionStore();

// Column configuration - ONLY these columns will be shown
const columnConfig = {
  id: {
    title: 'ID',
    width: '80px',
    filterable: true
  },
  child_name: {
    title: 'Child Name',
    width: '200px',
    filterable: true
  },
  gender: {
    title: 'gender',
    type: 'badge',
    filterType: 'select',
    filterOptions: [
      { value: '', label: 'All Gender' },
      { value: 'male', label: 'Male' },
      { value: 'female', label: 'Female' }
    ],
    filterable: true
  },
  phone_no: {
    title: 'Phone Number',
    width: '100px',
    filterable: false
  },
  relation: {
    title: 'Relation',
    filterable: true
  }

  //add
  // Only these 6 columns will be shown: ID, Name, Email, Image, Position, Status
  // Actions column will be auto-added because we have @edit and @view handlers
  // No other columns will appear (no created_at, updated_at, etc.)
}

// Table configuration
const tableConfig = {
  pagination: {
    enabled: true,
    pageSize: 10,
    pageSizes: [5, 10, 25, 50],
    showInfo: true,
    showSizeChanger: true
  }
}
</script>
<template>
  <DataTableProvider>
    <div class="p-4">
      <SmartDataTable title="Admission Management" :store="admissionStore" fetch-method="fetchAdmissions"
        :column-config="columnConfig" :table-config="tableConfig" :default-actions="[]" />
    </div>
  </DataTableProvider>
</template>
